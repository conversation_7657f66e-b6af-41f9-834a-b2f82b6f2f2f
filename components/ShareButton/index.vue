<template>
  <button
    :class="buttonClass"
    :disabled="isLoading"
    @click="handleShare"
    style="backdrop-filter: blur(34px);"
  >
    <div
      v-if="isLoading"
      class="i-svg-spinners:3-dots-fade w-4 h-4 pointer-events-none"
    ></div>
    <div v-else class="i-proicons:x-twitter w-4 h-4 pointer-events-none"></div>
    <span class="pointer-events-none">{{ buttonText }}</span>
  </button>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'

  const props = defineProps<{
    cardId: string
    type?: 'profile' | 'default' | 'popup'
    isDark?: boolean
    variant?: 'transparent' | 'colored'
  }>()

  const emit = defineEmits<{
    'share-popup': []
  }>()

  const isLoading = ref(false)

  // 计算按钮文字
  const buttonText = computed(() => {
    if (isLoading.value) {
      return 'Preparing...'
    }
    return 'Share'
  })

  // 计算按钮样式
  const buttonClass = computed(() => {
    const baseClass = 'fx-cer rounded-full py-2 px-4 gap-2 text-sm font-medium transition-all duration-200 select-none min-h-[40px] cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed'

    if (props.variant === 'transparent') {
      if (props.isDark) {
        return `${baseClass} bg-[#14141580] border border-[#27282D] text-[#FAF9F5] shadow-sm hover:bg-[#27282D] disabled:hover:bg-[#14141580]`
      } else {
        return `${baseClass} bg-[#FFFFFF]/60 border border-gray-200 text-gray-700 hover:bg-[#F5F5F5] disabled:hover:bg-[#FFFFFF]/60`
      }
    } else if (props.variant === 'colored') {
      // 使用与ProfileCard一致的颜色样式
      if (props.isDark) {
        return `${baseClass} bg-[#654D43] text-white hover:bg-[#654D43]/80 disabled:hover:bg-[#654D43]`
      } else {
        return `${baseClass} bg-[#CB7C5D] text-white hover:bg-[#CB7C5D]/80 disabled:hover:bg-[#CB7C5D]`
      }
    } else {
      return `${baseClass} bg-black text-white hover:bg-gray-800 disabled:hover:bg-black`
    }
  })

  const handleShare = async () => {
    if (isLoading.value) return

    // 如果type为popup，则触发分享弹窗事件
    if (props.type === 'popup') {
      emit('share-popup')
      return
    }

    // 开始准备状态，显示"Preparing..."
    isLoading.value = true

    try {
      // 等待2秒钟显示"Preparing..."状态
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 从DOM中获取用户名称
      let userName = 'Trevor Darrell' // 默认名称
      let isGitHubCard = false
      let isCompareCard = false
      let userName2 = ''

      // 尝试从GitHub Compare ShareCard中获取用户名称
      const githubCompareCardElement = document.querySelector('[data-card-id="share-card-github-compare"]')
      if (githubCompareCardElement) {
        isGitHubCard = true
        isCompareCard = true
        const nameElements = githubCompareCardElement.querySelectorAll('.font-bold')
        if (nameElements.length >= 2) {
          userName = nameElements[0].textContent?.trim() || 'User 1'
          userName2 = nameElements[1].textContent?.trim() || 'User 2'
        }
      }

      // 尝试从Scholar Compare ShareCard中获取用户名称
      if (!isGitHubCard) {
        const scholarCompareCardElement = document.querySelector('[data-card-id="share-card-compare"]')
        if (scholarCompareCardElement) {
          isCompareCard = true
          const nameElements = scholarCompareCardElement.querySelectorAll('.font-bold')
          if (nameElements.length >= 2) {
            userName = nameElements[0].textContent?.trim() || 'Researcher 1'
            userName2 = nameElements[1].textContent?.trim() || 'Researcher 2'
          }
        }
      }

      // 尝试从GitHub ShareCard中获取用户名称
      if (!isCompareCard) {
        const githubShareCardElement = document.querySelector('[data-card-id="share-card-github"]')
        if (githubShareCardElement) {
          isGitHubCard = true
          const nameElement = githubShareCardElement.querySelector('h2')
          if (nameElement && nameElement.textContent) {
            userName = nameElement.textContent.trim()
          }
        }
      }

      // 尝试从Scholar ShareCard中获取用户名称
      if (!isGitHubCard && !isCompareCard) {
        const shareCardElement = document.querySelector('[data-card-id="share-card"]')
        if (shareCardElement) {
          const nameElement = shareCardElement.querySelector('h2')
          if (nameElement && nameElement.textContent) {
            userName = nameElement.textContent.trim()
          }
        }
      }

      // 如果ShareCard中没有找到，尝试从ProfileCard中获取
      if (userName === 'Trevor Darrell' && !isCompareCard) {
        const profileCardElement = document.querySelector('.f-cer.w-full')
        if (profileCardElement) {
          const nameElement = profileCardElement.querySelector('.font-bold.text-xl, .font-bold.text-2xl')
          if (nameElement && nameElement.textContent) {
            userName = nameElement.textContent.trim()
          }
        }
      }

      // 根据卡片类型选择不同的文案模板
      let shareTexts: string[]

      if (isCompareCard && isGitHubCard) {
        // GitHub Compare Card
        shareTexts = [
          `${userName} vs ${userName2} GitHub developer comparison is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ #GitHub`,
          `GitHub developer comparison is here! ${userName} vs ${userName2}, from DINQ.me. Don't miss it! @dinq_io #DINQ #GitHub`,
          `${userName} vs ${userName2} GitHub developer evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ #GitHub`
        ]
      } else if (isCompareCard) {
        // Scholar Compare Card
        shareTexts = [
          `${userName} vs ${userName2} AI researcher comparison is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ`,
          `AI researcher comparison is here! ${userName} vs ${userName2}, from DINQ.me. Don't miss it! @dinq_io #DINQ`,
          `${userName} vs ${userName2} AI researcher evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ`
        ]
      } else if (isGitHubCard) {
        // GitHub Individual Card
        shareTexts = [
          `${userName}'s GitHub developer analysis is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ #GitHub`,
          `GitHub developer analysis is here! By ${userName}, from DINQ.me. Don't miss it! @dinq_io #DINQ #GitHub`,
          `${userName}'s GitHub developer evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ #GitHub`
        ]
      } else {
        // Scholar Individual Card
        shareTexts = [
          `${userName}'s AI talent assessment is out, from DINQ.me. Full of insights, check it out! @dinq_io #DINQ`,
          `AI talent analysis is here! By ${userName}, from DINQ.me. Don't miss it! @dinq_io #DINQ`,
          `${userName}'s AI talent evaluation, released by DINQ.me. Worth your attention! @dinq_io #DINQ`
        ]
      }

      // 随机选择一个文案
      const randomText = shareTexts[Math.floor(Math.random() * shareTexts.length)]

      // 获取当前页面URL并添加到分享文本中
      const currentUrl = window.location.href
      const shareTextWithUrl = `${randomText} ${currentUrl}`

      // 打开Twitter分享页面
      const twitterComposeUrl = 'https://twitter.com/intent/tweet'
      const shareText = encodeURIComponent(shareTextWithUrl)
      const shareUrl = `${twitterComposeUrl}?text=${shareText}`

      window.open(shareUrl, '_blank')

    } catch (error) {
      console.error('Share failed:', error)
    } finally {
      isLoading.value = false
    }
  }
</script>

<style scoped>
  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .share-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: rgb(192, 132, 108);
    border: none;
    border-radius: 20px;
    cursor: pointer;
    color: white;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .share-button:hover {
    background-color: rgb(172, 112, 88);
  }

  .share-button img {
    width: 20px;
    height: 20px;
  }
</style>
